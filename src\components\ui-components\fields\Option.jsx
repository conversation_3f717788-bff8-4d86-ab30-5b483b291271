import { Check } from 'lucide-react';
import React from 'react';

function Option({ label, isChecked = false, onClick = null, isLabelNRadioClose = false, className = '' }) {
  return (
    <div
      className={`m-0 w-full flex flex-shrink-0 items-center gap-2 cursor-pointer select-none ${
        !isLabelNRadioClose && 'justify-between'
      } ${className}`}
      onClick={onClick}
    >
      {!isLabelNRadioClose && label}
      {isChecked ? (
        <span className="flex flex-1 items-center justify-center rounded-full max-w-5 h-5 bg-accent2 overflow-hidden">
          <Check className="text-white" />
        </span>
      ) : (
        <span className="flex flex-1 items-center justify-center rounded-full max-w-5 h-5 border-2 border-accent2 overflow-hidden" />
      )}
      {isLabelNRadioClose && label}
    </div>
  );
}

export default Option;
