@import '../../../../../assets/scss/main.scss';

:global(.ui.dimmer) {
  -webkit-backdrop-filter: blur(1px) !important;
  backdrop-filter: blur(1px) !important;
  background-color: rgba(0, 0, 0, 0.65) !important;
}

.dropdownContent {
  padding: 2em;
  min-height: 520px;
  max-height: 520px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 2em;
  @include hide-scrollbar;
}

.inputField {
  width: 100%;
  padding: 1em 1.5em 1em 3em;
  border: 1px solid #e9eaeb;
  border-radius: 12px;
  background-color: #f5f5f5;
  font-size: 14px;
  color: #101828;

  &::placeholder {
    color: #717680;
  }

  &:focus {
    outline: none;
    border-color: #9ea5d1;
  }
}

.addBtn {
  border-radius: 9999px;
  background: $accentColor2 !important;
  color: $white !important;
  width: fit-content;
  flex-shrink: 0;
  border-radius: 1em !important;
  &:disabled {
    background: $grayColor1 !important;
    color: $black !important;
    cursor: not-allowed !important;
  }
}

.optionsContainer {
  display: flex;
  flex-direction: column;
  gap: 1em;
  .optionHeader {
    display: flex;
    align-items: center;
    gap: 0.5em;
    font-weight: 600;
    font-size: 1.1em;
    color: #101828;
  }
  .optionList {
    display: flex;
    flex-direction: column;
    gap: 0.2em;
    width: 100%;
  }
  .option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5em 1em;
    border-radius: 1em;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-size: 1.1em;
    &:hover {
      background-color: #f5f5f5;
    }

    &.selected {
      background-color: #f5f5f5;
    }
  }
}

.actionContainer {
  display: flex;
  justify-content: center;
  gap: 1.5em;
  padding: 1em;
  border-top: 1px solid #e9eaeb;
  background-color: #fdfdfd;

  .cancelButton {
    padding: 0.4em 1em !important;
    border-radius: 2em !important;
    background-color: $accentBgColor1 !important;
    border: 1px solid $accentBorder1 !important;
    color: $primaryColor !important;
    font-weight: 600 !important;
    &:hover {
      background-color: $accentHover1 !important;
    }
  }

  .updateButton {
    padding: 0.4em 1em !important;
    border-radius: 2em !important;
    background-color: rgba($accentColor2, 0.2) !important;
    font-weight: 600 !important;
    border: none !important;

    &:not(:disabled) {
      background-color: $accentColor2 !important;
      color: #ffffff !important;

      &:hover {
        background-color: $accentHover2 !important;
      }
    }
  }
}
