import React from 'react';
import ModalDropdown from '../ModalDropdown';
import <PERSON><PERSON>ield from '../AiField';
import { getFieldAlertObject } from '../../../../../utils/aiUtils';
import { NumericFormat } from 'react-number-format';
import { formatIndianCurrency } from '../../../../../utils/dateUtils';

function TallyGstFields({
  data,
  isReadOnly,
  index,
  gstLedgersUrl,
  formAction,
  SECTION,
  isSameState,
  handleOnPaste,
  handleBlur,
  isRateWiseGSt,
}) {
  if (isSameState) {
    return (
      <div className="only-4-column">
        {/* SGST Amount */}
        <AiField
          label="SGST Amount"
          isExactMatch={data?.exact_match?.sgst_amount}
          alertObject={getFieldAlertObject(data, 'sgst_amount')}
          name="sgst_amount"
          id={`sgst_amount_${index}`}
          alignSuggestionRight={true}
          copyText={formatIndianCurrency(data?.recommended_fields?.sgst_amount, true)}
          onPaste={(copyText, field) => handleOnPaste(copyText, field, index)}
          disabled={isReadOnly}
          renderCustomField={() => (
            <NumericFormat
              value={data?.sgst_amount ?? ''}
              thousandSeparator={true}
              thousandsGroupStyle="lakh"
              decimalScale={2}
              fixedDecimalScale
              allowNegative={true}
              className="input-field text-right"
              disabled={isReadOnly}
              onBlur={handleBlur}
              id={`sgst_amount_${index}`}
              onValueChange={(values) => {
                formAction('INDEX_FIELD_CHANGE', SECTION, 'sgst_amount', values.value, index);
              }}
            />
          )}
        />

        {/* SGST Ledger */}
        <div>
          <AiField
            label="Ledger"
            alertObject={getFieldAlertObject(data, 'sgst_ledger_name')}
            type="text"
            value={data?.sgst_ledger_name ?? ''}
            onChange={(e) => formAction('INDEX_FIELD_CHANGE', SECTION, 'sgst_ledger_name', e.target.value, index)}
            readOnly
            disabled={isReadOnly}
            name="sgst_ledger_name"
            id={`sgst_ledger_name_${index}`}
            required={Number(data?.sgst_amount) !== 0}
          />
          <ModalDropdown
            label="Select Ledger"
            onSelect={(option) =>
              formAction(
                'INDEX_UPDATE_SECTION',
                SECTION,
                null,
                {
                  sgst_ledger_name: option.label,
                  sgst_ledger_id: String(option.key),
                },
                index
              )
            }
            url={`${gstLedgersUrl}${isRateWiseGSt ? `&tax_rate=${data?.tax_rate}` : ''}&gst_type=sgst`}
            transformOptionsObj={{
              key: 'master_id',
              value: 'master_id',
              label: 'ledger',
            }}
            disabled={isReadOnly}
          />
        </div>

        {/* CGST Amount */}
        <AiField
          label="CGST Amount"
          isExactMatch={data?.exact_match?.cgst_amount}
          alertObject={getFieldAlertObject(data, 'cgst_amount')}
          copyText={formatIndianCurrency(data?.recommended_fields?.cgst_amount, true)}
          onPaste={(copyText, field) => handleOnPaste(copyText, field, index)}
          disabled={isReadOnly}
          name="cgst_amount"
          id={`cgst_amount_${index}`}
          alignSuggestionRight={true}
          renderCustomField={() => (
            <NumericFormat
              value={data?.cgst_amount ?? ''}
              thousandSeparator={true}
              thousandsGroupStyle="lakh"
              decimalScale={2}
              fixedDecimalScale
              allowNegative={true}
              className="input-field text-right"
              disabled={isReadOnly}
              onBlur={handleBlur}
              id={`cgst_amount_${index}`}
              onValueChange={(values) => {
                formAction('INDEX_FIELD_CHANGE', SECTION, 'cgst_amount', values.value, index);
              }}
            />
          )}
        />

        {/* CGST Ledger */}
        <div>
          <AiField
            label="Ledger"
            alertObject={getFieldAlertObject(data, 'cgst_ledger_name')}
            type="text"
            value={data?.cgst_ledger_name ?? ''}
            onChange={(e) => formAction('INDEX_FIELD_CHANGE', SECTION, 'cgst_ledger_name', e.target.value, index)}
            readOnly
            disabled={isReadOnly}
            name="cgst_ledger_name"
            id={`cgst_ledger_name_${index}`}
            required={Number(data?.cgst_amount) !== 0}
          />
          <ModalDropdown
            label="Select Ledger"
            onSelect={(option) =>
              formAction(
                'INDEX_UPDATE_SECTION',
                SECTION,
                null,
                {
                  cgst_ledger_name: option.label,
                  cgst_ledger_id: String(option.key),
                },
                index
              )
            }
            url={`${gstLedgersUrl}${isRateWiseGSt ? `&tax_rate=${data?.tax_rate}` : ''}&gst_type=cgst`}
            transformOptionsObj={{
              key: 'master_id',
              value: 'master_id',
              label: 'ledger',
            }}
            disabled={isReadOnly}
          />
        </div>
      </div>
    );
  }

  // Default IGST Amount and Ledger
  return (
    <>
      {/* IGST Amount */}
      <AiField
        label="IGST Amount"
        isExactMatch={data?.exact_match?.igst_amount}
        alertObject={getFieldAlertObject(data, 'igst_amount')}
        name="igst_amount"
        id={`igst_amount_${index}`}
        disabled={isReadOnly}
        copyText={formatIndianCurrency(data?.recommended_fields?.igst_amount, true)}
        onPaste={(copyText, field) => handleOnPaste(copyText, field, index)}
        alignSuggestionRight={true}
        renderCustomField={() => (
          <NumericFormat
            value={data?.igst_amount ?? ''}
            thousandSeparator={true}
            thousandsGroupStyle="lakh"
            decimalScale={2}
            fixedDecimalScale
            allowNegative={true}
            className="input-field text-right"
            disabled={isReadOnly}
            onBlur={handleBlur}
            id={`igst_amount_${index}`}
            onValueChange={(values) => {
              formAction('INDEX_FIELD_CHANGE', SECTION, 'igst_amount', values.value, index);
            }}
          />
        )}
      />

      {/* IGST Ledger */}
      <div>
        <AiField
          label="Ledger"
          alertObject={getFieldAlertObject(data, 'igst_ledger_name')}
          type="text"
          value={data?.igst_ledger_name ?? ''}
          onChange={(e) => formAction('INDEX_FIELD_CHANGE', SECTION, 'igst_ledger_name', e.target.value, index)}
          readOnly
          disabled={isReadOnly}
          name="igst_ledger_name"
          id={`igst_ledger_name_${index}`}
          required={Number(data?.igst_amount) !== 0}
        />
        <ModalDropdown
          label="Select Ledger"
          onSelect={(option) =>
            formAction(
              'INDEX_UPDATE_SECTION',
              SECTION,
              null,
              {
                igst_ledger_name: option.label,
                igst_ledger_id: String(option.key),
              },
              index
            )
          }
          url={`${gstLedgersUrl}${isRateWiseGSt ? `&tax_rate=${data?.tax_rate}` : ''}&gst_type=igst`}
          transformOptionsObj={{
            key: 'master_id',
            value: 'master_id',
            label: 'ledger',
          }}
          disabled={isReadOnly}
        />
      </div>
    </>
  );
}

export default TallyGstFields;
