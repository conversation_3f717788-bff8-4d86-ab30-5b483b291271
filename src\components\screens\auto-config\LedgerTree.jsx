import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronRight, FolderOutlined } from '@mui/icons-material';

function LedgerNode({ node, depth = 0 }) {
  const [expanded, setExpanded] = useState(false);

  const label = node.parent_group_name || node.parent_ledger_name || node.ledger_name;
  const hasChildren = node.ledgers && node.ledgers.length > 0;

  // If parent_ledger_name is null and has children, we'll render its children directly
  if (node.parent_ledger_name === null && hasChildren) {
    return (
      <div className="relative">
        {node.ledgers.map((child, idx) => (
          <LedgerNode key={idx} node={child} depth={depth} />
        ))}
      </div>
    );
  }

  return (
    <div className="relative">
      {/* Connecting lines for tree structure */}
      {depth > 0 && (
        <>
          {/* vertical line */}
          <div
            className="absolute left-0 top-0 w-px h-full bg-gray-300"
            style={{ left: `${(depth - 1) * 24 + 12}px` }}
          />
          {/* horizontal line */}
          <div className="absolute top-6 w-3 h-px bg-gray-300" style={{ left: `${(depth - 1) * 24 + 12}px`, top: hasChildren ? '' : '50%' }} />
        </>
      )}

      {/* Display ledger name */}
      <motion.div
        className={`flex items-center py-2 px-3 my-[2.5px] rounded-lg transition-all duration-200 relative group ${
          hasChildren ? 'cursor-pointer hover:bg-accent1-bg-hover hover:shadow-sm' : 'hover:bg-secondary-color'
        } ${expanded ? 'bg-accent1-bg shadow-sm' : ''}`}
        onClick={hasChildren ? () => setExpanded(!expanded) : undefined}
        style={{ marginLeft: `${depth * 24}px` }}
      >
        <div className={`flex items-center w-full ${hasChildren ? 'gap-3' : ''}`}>
          {hasChildren ? (
            //chevron expand / collapse
            <motion.div
              className="flex items-center justify-center w-6 h-6 text-accent2 transition-colors duration-200 group-hover:text-accent2-hover bg-white rounded-full shadow-sm border border-accent1-border"
              variants={{
                collapsed: { rotate: 0 },
                expanded: { rotate: 90 },
              }}
              animate={expanded ? 'expanded' : 'collapsed'}
              transition={{ duration: 0.2, ease: 'easeInOut' }}
            >
              <ChevronRight fontSize="small" />
            </motion.div>
          ) : (
            //dot
            <div className="flex items-center justify-center w-6 h-6">
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
            </div>
          )}

          <span
            className={`${depth === 0 ? 'font-semibold' : ''} text-base select-none transition-colors duration-200 text-primary-color group-hover:text-accent2`}
          >
            {label}
          </span>
        </div>
      </motion.div>

      {/* Children nodes */}
      <AnimatePresence>
        {expanded && hasChildren && (
          <motion.div
            variants={{
              hidden: { opacity: 0, height: 0 },
              visible: {
                opacity: 1,
                height: 'auto',
                transition: {
                  height: { duration: 0.3, ease: 'easeInOut' },
                  opacity: { duration: 0.2, delay: 0.1 },
                  staggerChildren: 0.05,
                  delayChildren: 0.1,
                },
              },
            }}
            initial="hidden"
            animate="visible"
            exit="hidden"
            className="overflow-hidden"
          >
            {node.ledgers.map((child, idx) => (
              <motion.div
                key={idx}
                variants={{
                  hidden: { opacity: 0, x: -10 },
                  visible: { opacity: 1, x: 0 },
                }}
                transition={{ duration: 0.2 }}
              >
                <LedgerNode node={child} depth={depth + 1} />
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

export default function LedgerTree({ data }) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, ease: 'easeOut' }}
    >
      {data && data.length > 0 ? (
        <div className="space-y-1">
          {data.map((node, idx) => (
            <LedgerNode key={idx} node={node} depth={0} />
          ))}
        </div>
      ) : (
        <div className="text-center py-8 text-primary-color absolute top-0 left-0 w-full h-full flex flex-col items-center justify-center">
          <FolderOutlined className="text-4xl mb-2 opacity-50 text-warning" />
          <p>No ledger data available</p>
        </div>
      )}
    </motion.div>
  );
}
