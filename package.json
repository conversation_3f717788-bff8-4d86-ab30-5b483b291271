{"name": "<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@tanstack/react-query": "^5.80.5", "apexcharts": "^4.4.0", "axios": "^1.7.7", "chart.js": "^4.4.6", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dompurify": "^3.2.3", "fomantic-ui-css": "^2.9.3", "formik": "^2.4.6", "js-cookie": "^3.0.5", "local-storage": "^2.0.0", "lodash": "^4.17.21", "lucide-react": "^0.479.0", "motion": "^12.6.3", "qs": "^6.14.0", "react": "^18.3.1", "react-apexcharts": "^1.7.0", "react-avatar": "^5.0.3", "react-chartjs-2": "^5.2.0", "react-datepicker": "^8.3.0", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-icons": "^5.4.0", "react-number-format": "^5.4.4", "react-router-dom": "^6.27.0", "react-toastify": "^10.0.6", "react-tooltip": "^5.28.0", "reactjs-otp-input": "^2.0.10", "semantic-ui-react": "^3.0.0-beta.2", "web-vitals": "^2.1.4", "yup": "^1.4.0"}, "scripts": {"start": "env-cmd -f .env react-scripts start", "start:development": "env-cmd -f .env.development react-scripts start", "start:staging": "env-cmd -f .env.staging react-scripts start", "start:production": "env-cmd -f .env.production react-scripts start", "build": "env-cmd -f .env react-scripts build", "build:development": "env-cmd -f .env.development react-scripts build", "build:staging": "env-cmd -f .env.staging react-scripts build", "build:production": "env-cmd -f .env.production react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write ."}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"transformIgnorePatterns": ["node_modules/(?!axios)"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "env-cmd": "^10.1.0", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "prettier": "^3.6.2", "react-scripts": "^5.0.1", "sass": "^1.83.4", "tailwindcss": "^3.4.17"}}